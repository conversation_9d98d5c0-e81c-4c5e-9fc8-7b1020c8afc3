# 精简版 Clash 配置 (<PERSON>homo 内核专用): Argo + 链式美国家宽
port: 7890
socks-port: 7891
allow-lan: true
mode: rule
log-level: info
ipv6: false

dns:
  enable: true
  listen: 0.0.0.0:53
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  nameserver:
    - *******
    - *******
  fallback:
    - *******
    - *******
  default-nameserver:
    - *******

# 所有的代理节点定义
proxies:
  # 1. 您保留的两个 Argo 节点 (将作为链式代理的入口/前置)
  - name: "vmess-443-argo"
    type: vmess
    server: **********
    port: 443
    uuid: 7e399106-a6c0-401c-bbe7-671f9fd2272e
    alterId: 0
    cipher: auto
    tls: true
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /7e399106-a6c0-401c-bbe7-671f9fd2272e-vm?ed=2048
      headers:
        Host: indie-growing-nhs-send.trycloudflare.com
    servername: indie-growing-nhs-send.trycloudflare.com

  - name: "vmess-8443-argo"
    type: vmess
    server: **********
    port: 8443
    uuid: 7e399106-a6c0-401c-bbe7-671f9fd2272e
    alterId: 0
    cipher: auto
    tls: true
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /7e399106-a6c0-401c-bbe7-671f9fd2272e-vm?ed=2048
      headers:
        Host: indie-growing-nhs-send.trycloudflare.com
    servername: indie-growing-nhs-send.trycloudflare.com
        
  # 2. 这是真正的链式代理节点定义 (Mihomo 语法)
  # 我们定义一个 Socks5 代理，并告诉它通过 Argo 节点去连接
  - name: "🇺🇸 [链式] Argo-443 -> 美国"
    type: socks5
    server: pool-sg.quarkip.io
    port: 7777
    username: "PanIwkVFRd-country-US-sessid-175414627310000"
    password: "FyOZi97L6CYMnov"
    udp: true
    # --- Mihomo 核心语法 ---
    # 指定使用哪个节点作为“跳板”
    dialer-proxy: "vmess-443-argo"

  - name: "🇺🇸 [链式] Argo-8443 -> 美国"
    type: socks5
    server: pool-sg.quarkip.io
    port: 7777
    username: "PanIwkVFRd-country-US-sessid-175414627310000"
    password: "FyOZi97L6CYMnov"
    udp: true
    # --- Mihomo 核心语法 ---
    dialer-proxy: "vmess-8443-argo"

  # 3. 为了方便测试，我们再定义一个不经过Argo、直连的美国家宽节点
  - name: "🇺🇸 [直连] 美国 Residential IP"
    type: socks5
    server: pool-sg.quarkip.io
    port: 7777
    username: "PanIwkVFRd-country-US-sessid-175414627310000"
    password: "FyOZi97L6CYMnov"
    udp: true


# 代理组的定义
proxy-groups:
  # 这是您在Clash中唯一需要手动选择的组
  - name: "🔰 节点选择"
    type: select
    proxies:
      # 将所有可用的选择都放在这里
      - "🇺🇸 [链式] Argo-443 -> 美国"
      - "🇺🇸 [链式] Argo-8443 -> 美国"
      - "vmess-443-argo"         # 单独使用Argo节点
      - "vmess-8443-argo"        # 单独使用Argo节点
      - "🇺🇸 [直连] 美国 Residential IP"
      - DIRECT                   # 直连

# 规则定义
rules:
  - GEOIP,CN,DIRECT
  - DOMAIN-SUFFIX,apple.com,DIRECT
  # 所有其他流量都走上面 "🔰 节点选择" 组里你所选的那个节点
  - MATCH,🔰 节点选择

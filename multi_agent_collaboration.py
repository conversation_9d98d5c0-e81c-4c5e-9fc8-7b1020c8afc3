import torch
import torch.nn as nn
import torch.nn.functional as F
import asyncio
import json
import time
from typing import Dict, List, Optional, Tuple, Union, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import threading
import queue
from collections import defaultdict, deque
import logging
import copy

from multi_head_latent_attention import MultiHeadLatentAttention
from transformer_pytorch import Transformer, MultiHeadAttention


class AgentRole(Enum):
    """定义不同的代理角色"""
    COORDINATOR = "coordinator"      # 协调器 - 分配任务和协调
    SPECIALIST = "specialist"        # 专家 - 专门处理特定类型任务  
    EVALUATOR = "evaluator"         # 评估器 - 评估结果质量
    MEDIATOR = "mediator"           # 调解器 - 处理冲突和整合意见
    MONITOR = "monitor"             # 监视器 - 监控系统状态


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


class MessageType(Enum):
    """消息类型"""
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    COORDINATION = "coordination"
    COLLABORATION = "collaboration"
    STATUS_UPDATE = "status_update"
    CONSENSUS = "consensus"
    CONFLICT_RESOLUTION = "conflict_resolution"


@dataclass
class Task:
    """任务定义"""
    id: str
    type: str
    content: str
    priority: TaskPriority
    requirements: Dict[str, Any] = field(default_factory=dict)
    deadline: Optional[float] = None
    dependencies: List[str] = field(default_factory=list)
    assigned_agents: List[str] = field(default_factory=list)
    status: str = "pending"
    result: Optional[Any] = None
    created_at: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Message:
    """代理间通信消息"""
    id: str
    sender: str
    receiver: str
    message_type: MessageType
    content: Any
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AgentState:
    """代理状态"""
    agent_id: str
    role: AgentRole
    status: str = "idle"  # idle, busy, error, offline
    current_tasks: List[str] = field(default_factory=list)
    capabilities: List[str] = field(default_factory=list)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    last_update: float = field(default_factory=time.time)


class AttentionBasedCollaborationModule(nn.Module):
    """基于注意力机制的协作模块"""
    
    def __init__(self, d_model: int = 512, num_heads: int = 8, num_latents: int = 16):
        super().__init__()
        self.d_model = d_model
        
        # 使用现有的多头潜在注意力机制
        self.collaboration_attention = MultiHeadLatentAttention(
            d_model=d_model,
            num_heads=num_heads,
            num_latents=num_latents,
            dropout=0.1,
            use_residual=True,
            use_layer_norm=True
        )
        
        # 代理表示嵌入
        self.agent_embedding = nn.Embedding(100, d_model)  # 支持最多100个代理
        self.role_embedding = nn.Embedding(len(AgentRole), d_model)
        self.task_embedding = nn.Linear(d_model, d_model)
        
        # 协作决策层
        self.collaboration_mlp = nn.Sequential(
            nn.Linear(d_model, d_model * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model * 2, d_model),
            nn.Dropout(0.1)
        )
        
        # 输出投影
        self.decision_head = nn.Linear(d_model, 64)  # 决策向量
        self.confidence_head = nn.Linear(d_model, 1)  # 置信度
        
    def forward(self, agent_states: torch.Tensor, task_context: torch.Tensor, 
                agent_ids: torch.Tensor, roles: torch.Tensor, 
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播 - 计算协作决策
        
        Args:
            agent_states: [batch_size, num_agents, d_model] - 代理状态表示
            task_context: [batch_size, seq_len, d_model] - 任务上下文
            agent_ids: [batch_size, num_agents] - 代理ID
            roles: [batch_size, num_agents] - 代理角色
            mask: 可选的掩码
            
        Returns:
            decisions: [batch_size, num_agents, 64] - 协作决策
            confidence: [batch_size, num_agents, 1] - 决策置信度
        """
        batch_size, num_agents, _ = agent_states.shape
        
        # 添加代理和角色嵌入
        agent_emb = self.agent_embedding(agent_ids)
        role_emb = self.role_embedding(roles)
        
        # 组合表示
        enhanced_states = agent_states + agent_emb + role_emb
        
        # 将任务上下文和代理状态合并
        combined_input = torch.cat([task_context, enhanced_states], dim=1)
        
        # 应用注意力机制进行协作推理
        attended_output = self.collaboration_attention(combined_input, mask)
        
        # 提取代理相关的输出
        agent_output = attended_output[:, -num_agents:, :]
        
        # 应用协作MLP
        collaborative_features = self.collaboration_mlp(agent_output)
        
        # 生成决策和置信度
        decisions = self.decision_head(collaborative_features)
        confidence = torch.sigmoid(self.confidence_head(collaborative_features))
        
        return decisions, confidence


class BaseAgent(ABC):
    """基础代理类"""
    
    def __init__(self, agent_id: str, role: AgentRole, capabilities: List[str]):
        self.agent_id = agent_id
        self.role = role
        self.capabilities = capabilities
        self.state = AgentState(agent_id, role, capabilities=capabilities)
        self.message_queue = asyncio.Queue()
        self.task_history = deque(maxlen=1000)
        self.performance_metrics = defaultdict(float)
        self.logger = logging.getLogger(f"Agent-{agent_id}")
        
    @abstractmethod
    async def process_task(self, task: Task) -> Any:
        """处理任务的抽象方法"""
        pass
    
    @abstractmethod
    async def handle_message(self, message: Message) -> Optional[Message]:
        """处理消息的抽象方法"""
        pass
    
    async def send_message(self, receiver: str, message_type: MessageType, 
                          content: Any, collaboration_system) -> None:
        """发送消息"""
        message = Message(
            id=f"{self.agent_id}-{time.time()}-{hash(content)}",
            sender=self.agent_id,
            receiver=receiver,
            message_type=message_type,
            content=content
        )
        await collaboration_system.route_message(message)
    
    async def update_state(self, **kwargs):
        """更新代理状态"""
        for key, value in kwargs.items():
            if hasattr(self.state, key):
                setattr(self.state, key, value)
        self.state.last_update = time.time()
    
    def get_performance_summary(self) -> Dict[str, float]:
        """获取性能摘要"""
        return dict(self.performance_metrics)


class CoordinatorAgent(BaseAgent):
    """协调器代理 - 负责任务分配和系统协调"""
    
    def __init__(self, agent_id: str):
        super().__init__(agent_id, AgentRole.COORDINATOR, 
                        ["task_assignment", "resource_allocation", "workflow_management"])
        self.task_queue = asyncio.Queue()
        self.agent_workloads = defaultdict(int)
        
    async def process_task(self, task: Task) -> Any:
        """协调器处理任务 - 主要是分配和管理"""
        self.logger.info(f"Coordinating task {task.id}: {task.type}")
        
        # 分析任务需求
        required_capabilities = task.requirements.get("capabilities", [])
        
        # 选择合适的代理
        suitable_agents = await self._select_agents(required_capabilities, task.priority)
        
        if not suitable_agents:
            return {"status": "failed", "reason": "No suitable agents available"}
        
        # 分配任务
        task.assigned_agents = suitable_agents
        task.status = "assigned"
        
        return {
            "status": "coordinated",
            "assigned_agents": suitable_agents,
            "coordination_strategy": self._determine_strategy(task)
        }
    
    async def handle_message(self, message: Message) -> Optional[Message]:
        """处理协调相关消息"""
        if message.message_type == MessageType.TASK_REQUEST:
            # 处理任务请求
            await self.task_queue.put(message.content)
            return Message(
                id=f"coord-response-{time.time()}",
                sender=self.agent_id,
                receiver=message.sender,
                message_type=MessageType.COORDINATION,
                content={"status": "task_queued", "queue_position": self.task_queue.qsize()}
            )
        return None
    
    async def _select_agents(self, required_capabilities: List[str], 
                           priority: TaskPriority) -> List[str]:
        """选择合适的代理"""
        # 这里应该与协作系统交互获取代理信息
        # 简化实现
        suitable_agents = []
        # 基于能力、负载和优先级选择代理
        return suitable_agents
    
    def _determine_strategy(self, task: Task) -> str:
        """确定协作策略"""
        if task.priority == TaskPriority.CRITICAL:
            return "parallel_execution"
        elif len(task.assigned_agents) > 1:
            return "collaborative"
        else:
            return "single_agent"


class SpecialistAgent(BaseAgent):
    """专家代理 - 专门处理特定领域的任务"""
    
    def __init__(self, agent_id: str, specialty: str, expert_capabilities: List[str]):
        super().__init__(agent_id, AgentRole.SPECIALIST, expert_capabilities)
        self.specialty = specialty
        self.knowledge_base = {}
        self.learning_buffer = deque(maxlen=100)
        
    async def process_task(self, task: Task) -> Any:
        """专家处理任务"""
        self.logger.info(f"Processing specialized task {task.id} in {self.specialty}")
        
        # 检查是否在专业领域内
        if not self._is_task_in_specialty(task):
            return {"status": "declined", "reason": f"Task outside specialty: {self.specialty}"}
        
        # 应用专业知识处理任务
        result = await self._apply_expertise(task)
        
        # 学习和更新知识库
        self._update_knowledge(task, result)
        
        return {
            "status": "completed",
            "result": result,
            "specialty": self.specialty,
            "confidence": self._calculate_confidence(task, result)
        }
    
    async def handle_message(self, message: Message) -> Optional[Message]:
        """处理专家相关消息"""
        if message.message_type == MessageType.COLLABORATION:
            # 参与协作
            return await self._collaborate(message.content)
        return None
    
    def _is_task_in_specialty(self, task: Task) -> bool:
        """检查任务是否在专业领域内"""
        return task.type in self.capabilities or self.specialty in task.requirements.get("specialties", [])
    
    async def _apply_expertise(self, task: Task) -> Any:
        """应用专业知识"""
        # 模拟专业处理
        processing_time = 0.1  # 模拟处理时间
        await asyncio.sleep(processing_time)
        
        return {
            "processed_by": self.specialty,
            "task_analysis": f"Analyzed {task.type} using {self.specialty} expertise",
            "recommendations": ["recommendation1", "recommendation2"]
        }
    
    def _update_knowledge(self, task: Task, result: Any):
        """更新知识库"""
        knowledge_key = f"{task.type}_{task.priority.name}"
        if knowledge_key not in self.knowledge_base:
            self.knowledge_base[knowledge_key] = []
        
        self.knowledge_base[knowledge_key].append({
            "task_id": task.id,
            "result": result,
            "timestamp": time.time()
        })
        
        self.learning_buffer.append((task, result))
    
    def _calculate_confidence(self, task: Task, result: Any) -> float:
        """计算结果置信度"""
        # 基于历史经验和任务复杂度计算置信度
        base_confidence = 0.8
        experience_factor = min(len(self.task_history) / 100, 0.2)
        return base_confidence + experience_factor
    
    async def _collaborate(self, collaboration_request: Dict) -> Message:
        """参与协作"""
        return Message(
            id=f"collab-{self.agent_id}-{time.time()}",
            sender=self.agent_id,
            receiver=collaboration_request.get("requester"),
            message_type=MessageType.COLLABORATION,
            content={
                "contribution": f"Specialized input from {self.specialty}",
                "expertise_level": "high",
                "collaboration_id": collaboration_request.get("collaboration_id")
            }
        )


class EvaluatorAgent(BaseAgent):
    """评估器代理 - 评估结果质量和性能"""
    
    def __init__(self, agent_id: str):
        super().__init__(agent_id, AgentRole.EVALUATOR, 
                        ["quality_assessment", "performance_evaluation", "result_validation"])
        self.evaluation_history = deque(maxlen=1000)
        self.quality_thresholds = {
            "accuracy": 0.8,
            "completeness": 0.9,
            "efficiency": 0.7
        }
        
    async def process_task(self, task: Task) -> Any:
        """评估结果质量"""
        self.logger.info(f"Evaluating task results for {task.id}")
        
        if not task.result:
            return {"status": "error", "reason": "No result to evaluate"}
        
        # 多维度评估
        evaluation = {
            "accuracy": await self._evaluate_accuracy(task),
            "completeness": await self._evaluate_completeness(task),
            "efficiency": await self._evaluate_efficiency(task),
            "overall_quality": 0.0
        }
        
        # 计算综合质量分数
        evaluation["overall_quality"] = sum(evaluation.values()) / 3
        
        # 生成改进建议
        suggestions = self._generate_improvement_suggestions(evaluation)
        
        self.evaluation_history.append((task.id, evaluation))
        
        return {
            "status": "evaluated",
            "evaluation": evaluation,
            "passed_quality_check": evaluation["overall_quality"] >= 0.75,
            "improvement_suggestions": suggestions
        }
    
    async def handle_message(self, message: Message) -> Optional[Message]:
        """处理评估相关消息"""
        if message.message_type == MessageType.TASK_RESPONSE:
            # 自动评估任务响应
            evaluation_result = await self._quick_evaluation(message.content)
            return Message(
                id=f"eval-{time.time()}",
                sender=self.agent_id,
                receiver=message.sender,
                message_type=MessageType.STATUS_UPDATE,
                content=evaluation_result
            )
        return None
    
    async def _evaluate_accuracy(self, task: Task) -> float:
        """评估准确性"""
        # 模拟准确性评估
        await asyncio.sleep(0.05)
        return 0.85  # 模拟分数
    
    async def _evaluate_completeness(self, task: Task) -> float:
        """评估完整性"""
        result = task.result
        if isinstance(result, dict):
            required_fields = task.requirements.get("required_fields", [])
            if required_fields:
                completeness = len([f for f in required_fields if f in result]) / len(required_fields)
                return completeness
        return 0.9  # 默认分数
    
    async def _evaluate_efficiency(self, task: Task) -> float:
        """评估效率"""
        if task.deadline and hasattr(task, 'completion_time'):
            time_ratio = task.completion_time / (task.deadline - task.created_at)
            return max(0, 1 - time_ratio)  # 越快完成分数越高
        return 0.8  # 默认分数
    
    def _generate_improvement_suggestions(self, evaluation: Dict[str, float]) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        for metric, score in evaluation.items():
            if metric != "overall_quality" and score < self.quality_thresholds.get(metric, 0.8):
                suggestions.append(f"Improve {metric}: current score {score:.2f}")
        
        return suggestions
    
    async def _quick_evaluation(self, content: Any) -> Dict[str, Any]:
        """快速评估"""
        return {
            "quick_assessment": "satisfactory",
            "confidence": 0.8,
            "timestamp": time.time()
        }


class MultiAgentCollaborationSystem:
    """多代理协作系统核心"""
    
    def __init__(self, collaboration_config: Dict[str, Any] = None):
        self.agents: Dict[str, BaseAgent] = {}
        self.tasks: Dict[str, Task] = {}
        self.message_bus = asyncio.Queue()
        self.collaboration_module = AttentionBasedCollaborationModule()
        self.active_collaborations: Dict[str, Dict] = {}
        self.system_state = "initialized"
        self.config = collaboration_config or {}
        self.logger = logging.getLogger("CollaborationSystem")
        
        # 性能监控
        self.metrics = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "active_agents": 0,
            "average_response_time": 0.0,
            "collaboration_success_rate": 0.0
        }
        
        # 启动系统组件
        self._initialize_system()
    
    def _initialize_system(self):
        """初始化系统"""
        self.logger.info("Initializing Multi-Agent Collaboration System")
        
        # 创建默认代理
        self._create_default_agents()
        
        # 启动消息处理循环
        asyncio.create_task(self._message_processing_loop())
        
        self.system_state = "running"
        self.logger.info("System initialization completed")
    
    def _create_default_agents(self):
        """创建默认代理"""
        # 协调器
        coordinator = CoordinatorAgent("coordinator-001")
        self.add_agent(coordinator)
        
        # 专家代理
        nlp_specialist = SpecialistAgent("nlp-specialist-001", "natural_language_processing", 
                                       ["text_analysis", "language_modeling", "translation"])
        vision_specialist = SpecialistAgent("vision-specialist-001", "computer_vision", 
                                          ["image_recognition", "object_detection", "image_generation"])
        self.add_agent(nlp_specialist)
        self.add_agent(vision_specialist)
        
        # 评估器
        evaluator = EvaluatorAgent("evaluator-001")
        self.add_agent(evaluator)
    
    async def add_agent(self, agent: BaseAgent):
        """添加代理到系统"""
        self.agents[agent.agent_id] = agent
        self.metrics["active_agents"] = len(self.agents)
        self.logger.info(f"Added agent {agent.agent_id} with role {agent.role.value}")
        
        # 启动代理消息处理
        asyncio.create_task(self._agent_message_handler(agent))
    
    async def remove_agent(self, agent_id: str):
        """从系统中移除代理"""
        if agent_id in self.agents:
            agent = self.agents[agent_id]
            await agent.update_state(status="offline")
            del self.agents[agent_id]
            self.metrics["active_agents"] = len(self.agents)
            self.logger.info(f"Removed agent {agent_id}")
    
    async def submit_task(self, task: Task) -> str:
        """提交任务到系统"""
        self.tasks[task.id] = task
        self.metrics["total_tasks"] += 1
        
        self.logger.info(f"Submitted task {task.id}: {task.type} with priority {task.priority.name}")
        
        # 发送任务给协调器
        await self._send_to_coordinator(task)
        
        return task.id
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            return {
                "id": task.id,
                "status": task.status,
                "assigned_agents": task.assigned_agents,
                "progress": self._calculate_progress(task),
                "result": task.result
            }
        return None
    
    async def route_message(self, message: Message):
        """路由消息"""
        await self.message_bus.put(message)
    
    async def initiate_collaboration(self, task_id: str, participating_agents: List[str]) -> str:
        """启动多代理协作"""
        collaboration_id = f"collab-{task_id}-{time.time()}"
        
        self.active_collaborations[collaboration_id] = {
            "task_id": task_id,
            "participants": participating_agents,
            "status": "active",
            "created_at": time.time(),
            "messages": []
        }
        
        # 通知参与的代理
        for agent_id in participating_agents:
            if agent_id in self.agents:
                await self.agents[agent_id].send_message(
                    agent_id,
                    MessageType.COLLABORATION,
                    {
                        "collaboration_id": collaboration_id,
                        "task_id": task_id,
                        "participants": participating_agents
                    },
                    self
                )
        
        self.logger.info(f"Initiated collaboration {collaboration_id} for task {task_id}")
        return collaboration_id
    
    async def _message_processing_loop(self):
        """消息处理循环"""
        while self.system_state == "running":
            try:
                # 等待消息
                message = await asyncio.wait_for(self.message_bus.get(), timeout=1.0)
                
                # 路由到目标代理
                if message.receiver in self.agents:
                    await self.agents[message.receiver].message_queue.put(message)
                else:
                    self.logger.warning(f"Message to unknown agent: {message.receiver}")
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error in message processing: {e}")
    
    async def _agent_message_handler(self, agent: BaseAgent):
        """代理消息处理器"""
        while self.system_state == "running":
            try:
                # 等待代理消息
                message = await asyncio.wait_for(agent.message_queue.get(), timeout=1.0)
                
                # 让代理处理消息
                response = await agent.handle_message(message)
                
                # 如果有响应，路由回去
                if response:
                    await self.route_message(response)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error in agent {agent.agent_id} message handling: {e}")
    
    async def _send_to_coordinator(self, task: Task):
        """发送任务给协调器"""
        coordinator_agents = [a for a in self.agents.values() if a.role == AgentRole.COORDINATOR]
        
        if coordinator_agents:
            coordinator = coordinator_agents[0]  # 使用第一个协调器
            message = Message(
                id=f"task-{task.id}-{time.time()}",
                sender="system",
                receiver=coordinator.agent_id,
                message_type=MessageType.TASK_REQUEST,
                content=task
            )
            await self.route_message(message)
        else:
            self.logger.warning("No coordinator available for task assignment")
    
    def _calculate_progress(self, task: Task) -> float:
        """计算任务进度"""
        if task.status == "completed":
            return 1.0
        elif task.status == "in_progress":
            return 0.5  # 简化的进度计算
        elif task.status == "assigned":
            return 0.2
        else:
            return 0.0
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        agent_states = {}
        for agent_id, agent in self.agents.items():
            agent_states[agent_id] = {
                "role": agent.role.value,
                "status": agent.state.status,
                "current_tasks": len(agent.state.current_tasks),
                "capabilities": agent.capabilities
            }
        
        return {
            "system_state": self.system_state,
            "metrics": self.metrics,
            "agents": agent_states,
            "active_tasks": len([t for t in self.tasks.values() if t.status != "completed"]),
            "active_collaborations": len(self.active_collaborations)
        }
    
    async def shutdown(self):
        """关闭系统"""
        self.logger.info("Shutting down Multi-Agent Collaboration System")
        self.system_state = "shutting_down"
        
        # 通知所有代理
        for agent in self.agents.values():
            await agent.update_state(status="offline")
        
        self.system_state = "stopped"
        self.logger.info("System shutdown completed")


# 使用示例和测试
async def example_usage():
    """使用示例"""
    print("🚀 Multi-Agent Collaboration System Demo")
    print("=" * 50)
    
    # 创建协作系统
    system = MultiAgentCollaborationSystem()
    
    # 等待系统初始化
    await asyncio.sleep(0.1)
    
    # 创建测试任务
    tasks = [
        Task(
            id="task-001",
            type="text_analysis",
            content="Analyze the sentiment of customer reviews",
            priority=TaskPriority.HIGH,
            requirements={"capabilities": ["text_analysis"], "specialties": ["natural_language_processing"]}
        ),
        Task(
            id="task-002", 
            type="image_recognition",
            content="Identify objects in uploaded images",
            priority=TaskPriority.MEDIUM,
            requirements={"capabilities": ["image_recognition"], "specialties": ["computer_vision"]}
        ),
        Task(
            id="task-003",
            type="complex_analysis",
            content="Multi-modal analysis of social media content",
            priority=TaskPriority.CRITICAL,
            requirements={"capabilities": ["text_analysis", "image_recognition"], 
                         "collaboration_required": True}
        )
    ]
    
    # 提交任务
    print("\n📋 Submitting tasks...")
    task_ids = []
    for task in tasks:
        task_id = await system.submit_task(task)
        task_ids.append(task_id)
        print(f"  ✅ Submitted {task.type} (ID: {task_id})")
    
    # 等待处理
    print("\n⏳ Processing tasks...")
    await asyncio.sleep(2.0)
    
    # 检查任务状态
    print("\n📊 Task Status:")
    for task_id in task_ids:
        status = await system.get_task_status(task_id)
        if status:
            print(f"  Task {task_id}: {status['status']} (Progress: {status['progress']*100:.1f}%)")
    
    # 显示系统状态
    print("\n🔍 System Status:")
    system_status = await system.get_system_status()
    print(f"  Active agents: {len(system_status['agents'])}")
    print(f"  Active tasks: {system_status['active_tasks']}")
    print(f"  Total tasks processed: {system_status['metrics']['total_tasks']}")
    
    print("\n🎯 Agent Details:")
    for agent_id, agent_info in system_status['agents'].items():
        print(f"  {agent_id} ({agent_info['role']}): {agent_info['status']}")
    
    # 启动协作示例
    print("\n🤝 Initiating collaboration for complex task...")
    collaboration_id = await system.initiate_collaboration(
        "task-003", 
        ["nlp-specialist-001", "vision-specialist-001", "evaluator-001"]
    )
    print(f"  Collaboration {collaboration_id} started")
    
    # 等待协作完成
    await asyncio.sleep(1.0)
    
    # 最终状态
    print("\n📈 Final System Metrics:")
    final_status = await system.get_system_status()
    print(f"  Total tasks: {final_status['metrics']['total_tasks']}")
    print(f"  Active collaborations: {final_status['active_collaborations']}")
    
    # 关闭系统
    await system.shutdown()
    print("\n✅ Demo completed!")


def test_attention_collaboration():
    """测试基于注意力的协作模块"""
    print("\n🧠 Testing Attention-based Collaboration Module")
    print("-" * 45)
    
    # 创建模块
    collab_module = AttentionBasedCollaborationModule(d_model=512, num_heads=8, num_latents=16)
    
    # 模拟数据
    batch_size, num_agents, seq_len = 2, 4, 10
    
    agent_states = torch.randn(batch_size, num_agents, 512)
    task_context = torch.randn(batch_size, seq_len, 512)
    agent_ids = torch.randint(0, 10, (batch_size, num_agents))
    roles = torch.randint(0, len(AgentRole), (batch_size, num_agents))
    
    # 前向传播
    with torch.no_grad():
        decisions, confidence = collab_module(agent_states, task_context, agent_ids, roles)
    
    print(f"  Input shapes:")
    print(f"    Agent states: {agent_states.shape}")
    print(f"    Task context: {task_context.shape}")
    print(f"  Output shapes:")
    print(f"    Decisions: {decisions.shape}")
    print(f"    Confidence: {confidence.shape}")
    print(f"  Confidence range: [{confidence.min().item():.3f}, {confidence.max().item():.3f}]")
    
    # 模型信息
    total_params = sum(p.numel() for p in collab_module.parameters())
    print(f"  Model parameters: {total_params:,}")
    
    print("  ✅ Attention collaboration module test passed!")


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    print("🎯 Multi-Agent Collaboration System - Comprehensive Test")
    print("=" * 60)
    
    # 测试注意力协作模块
    test_attention_collaboration()
    
    # 运行使用示例
    asyncio.run(example_usage())
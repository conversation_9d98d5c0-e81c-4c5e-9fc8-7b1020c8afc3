#!/usr/bin/env python3
"""
简化版RAG系统测试
"""

from rag_system import create_sample_knowledge_base

def test_rag():
    """测试RAG系统"""
    print("创建RAG系统...")
    rag = create_sample_knowledge_base()
    
    # 调试信息
    print(f"词汇表大小: {len(rag.vocabulary)}")
    print(f"文档数量: {len(rag.documents)}")
    print(f"TF-IDF向量数量: {len(rag.tf_idf_vectors)}")
    
    # 显示词汇表前10个词
    print(f"词汇表样例: {list(rag.vocabulary)[:10]}")
    
    # 测试单个查询
    queries = [
        "Python编程语言",
        "机器学习算法",
        "深度学习应用",
        "数据科学工具"
    ]
    
    for query in queries:
        print(f"\n{'='*50}")
        print(f"查询: {query}")
        print("-" * 50)
        
        # 显示查询预处理结果
        query_words = rag.preprocess_text(query)
        print(f"查询关键词: {query_words}")
        
        # 执行检索
        relevant_docs = rag.retrieve_relevant_docs(query, top_k=3)
        
        print(f"检索结果:")
        for i, (doc_id, text, score) in enumerate(relevant_docs):
            print(f"{i+1}. {doc_id}: {score:.4f} - {text[:60]}...")

if __name__ == "__main__":
    test_rag()
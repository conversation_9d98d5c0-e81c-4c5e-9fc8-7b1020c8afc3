#!/usr/bin/env python3
"""
RAG系统完整演示
"""

from rag_system import create_sample_knowledge_base

def demo_rag():
    """RAG系统演示"""
    print("🤖 RAG (检索增强生成) 系统演示")
    print("=" * 60)
    
    # 创建RAG系统
    print("正在初始化RAG系统...")
    rag = create_sample_knowledge_base()
    
    print(f"✅ RAG系统已初始化")
    print(f"📚 知识库包含 {len(rag.documents)} 个文档")
    print(f"📖 词汇表大小: {len(rag.vocabulary)} 个词汇")
    
    # 预设问题演示
    demo_questions = [
        ("Python有什么特点？", "查询编程语言相关信息"),
        ("机器学习算法", "查询AI算法信息"),
        ("深度学习应用", "查询深度学习应用场景"),
        ("数据科学", "查询数据科学相关信息"),
        ("云计算优势", "查询云计算相关信息"),
        ("区块链技术", "查询区块链相关信息")
    ]
    
    print("\n🔍 开始演示检索功能:")
    print("=" * 60)
    
    for i, (question, description) in enumerate(demo_questions, 1):
        print(f"\n📝 演示 {i}: {description}")
        print(f"❓ 问题: {question}")
        print("-" * 40)
        
        # 检索相关文档
        relevant_docs = rag.retrieve_relevant_docs(question, top_k=2)
        
        print("🎯 检索结果:")
        for rank, (doc_id, text, score) in enumerate(relevant_docs, 1):
            print(f"  {rank}. 文档 {doc_id} (相似度: {score:.3f})")
            print(f"     📄 {text[:80]}...")
        
        # 生成答案
        if relevant_docs and relevant_docs[0][2] > 0.1:  # 相似度阈值
            print(f"\n💡 答案: {relevant_docs[0][1]}")
        else:
            print(f"\n❌ 未找到足够相关的信息")
    
    print(f"\n{'='*60}")
    print("🎉 RAG系统演示完成！")
    print("\n📋 系统特点:")
    print("- ✅ 基于TF-IDF的文档检索")
    print("- ✅ 余弦相似度匹配")
    print("- ✅ 支持中英文混合文本")
    print("- ✅ 可扩展的知识库")
    
    print("\n🔧 技术实现:")
    print("- 文本预处理和分词")
    print("- TF-IDF向量化")
    print("- 相似度计算和排序")
    print("- 检索增强生成")

if __name__ == "__main__":
    demo_rag()
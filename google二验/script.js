// Student Information System - Interactive Features
// Author: <PERSON><PERSON>
// Modern JavaScript with ES6+ features

class StudentPortal {
    constructor() {
        this.currentSection = 'student-center';
        this.isLoading = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupMobileMenu();
        this.setupTableInteractions();
        this.setupNotifications();
        this.loadUserPreferences();
    }

    setupEventListeners() {
        // Navigation menu interactions
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => this.handleNavigation(e));
        });

        // Header button interactions
        const headerButtons = document.querySelectorAll('.icon-btn');
        headerButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleHeaderAction(e));
        });

        // Action buttons
        const actionButtons = document.querySelectorAll('.btn');
        actionButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleActionButton(e));
        });

        // Logout button
        const logoutBtn = document.querySelector('.logout-btn-sidebar');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.handleLogout());
        }

        // Keyboard navigation
        document.addEventListener('keydown', (e) => this.handleKeyboardNavigation(e));
    }

    setupMobileMenu() {
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        if (mobileToggle && sidebar) {
            mobileToggle.addEventListener('click', () => {
                sidebar.classList.toggle('mobile-open');
                mobileToggle.classList.toggle('active');
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!sidebar.contains(e.target) && !mobileToggle.contains(e.target)) {
                    sidebar.classList.remove('mobile-open');
                    mobileToggle.classList.remove('active');
                }
            });
        }
    }

    setupTableInteractions() {
        // Make tables sortable
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                if (header.textContent.includes('▲')) {
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', () => this.sortTable(table, index));
                }
            });
        });

        // Add hover effects to table rows
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', () => {
                row.style.backgroundColor = '#e3f2fd';
            });
            row.addEventListener('mouseleave', () => {
                row.style.backgroundColor = '';
            });
        });
    }

    setupNotifications() {
        // Simulate real-time notifications
        setTimeout(() => {
            this.showNotification('Welcome back, Chuan! You have 3 new messages.', 'info');
        }, 2000);

        // Check for enrollment deadlines
        this.checkEnrollmentDeadlines();
    }

    handleNavigation(e) {
        e.preventDefault();
        const button = e.currentTarget;
        const section = button.dataset.section;
        
        if (section && section !== this.currentSection) {
            this.switchSection(section);
            this.updateActiveNavItem(button);
        }
    }

    switchSection(section) {
        this.showLoading();
        
        // Simulate API call delay
        setTimeout(() => {
            this.currentSection = section;
            this.updateContent(section);
            this.hideLoading();
            
            // Update URL without page reload
            history.pushState({section}, '', `#${section}`);
        }, 500);
    }

    updateActiveNavItem(activeButton) {
        // Remove active class from all nav items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to current item
        activeButton.closest('.nav-item').classList.add('active');
    }

    updateContent(section) {
        const content = document.querySelector('.content');
        const breadcrumb = document.querySelector('.breadcrumb');
        
        // Update breadcrumb
        const sectionNames = {
            'student-center': 'Student Center',
            'messages': 'Messages',
            'holds': 'Academic Holds',
            'todo': 'To Do List',
            'payment': 'Payment Center',
            'enrollment': 'Course Enrollment',
            'records': 'Academic Records',
            'finances': 'Financial Information',
            'admissions': 'Admissions',
            'personal': 'Personal Information',
            'alerts': 'Campus Alerts',
            'other': 'Other Services'
        };
        
        if (breadcrumb) {
            breadcrumb.innerHTML = `<span>${sectionNames[section] || 'Student Center'}</span> > <span>Dashboard</span>`;
        }
        
        // Add fade effect
        content.style.opacity = '0';
        setTimeout(() => {
            content.style.opacity = '1';
        }, 100);
    }

    handleHeaderAction(e) {
        const button = e.currentTarget;
        const action = button.classList.contains('home-btn') ? 'home' :
                      button.classList.contains('help-btn') ? 'help' : 'logout';
        
        switch (action) {
            case 'home':
                this.switchSection('student-center');
                break;
            case 'help':
                this.showHelpModal();
                break;
            case 'logout':
                this.handleLogout();
                break;
        }
    }

    handleActionButton(e) {
        const button = e.currentTarget;
        const action = button.textContent.trim();
        
        this.showLoading();
        
        setTimeout(() => {
            this.hideLoading();
            this.showNotification(`${action} feature activated!`, 'success');
        }, 1000);
    }

    handleLogout() {
        if (confirm('Are you sure you want to sign out?')) {
            this.showLoading();
            setTimeout(() => {
                this.showNotification('Signing out...', 'info');
                // In a real app, this would redirect to login page
            }, 1000);
        }
    }

    sortTable(table, columnIndex) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        const sortedRows = rows.sort((a, b) => {
            const aText = a.cells[columnIndex].textContent.trim();
            const bText = b.cells[columnIndex].textContent.trim();
            
            // Try to parse as numbers first
            const aNum = parseFloat(aText);
            const bNum = parseFloat(bText);
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return aNum - bNum;
            }
            
            // Sort as strings
            return aText.localeCompare(bText);
        });
        
        // Clear and re-append sorted rows
        tbody.innerHTML = '';
        sortedRows.forEach(row => tbody.appendChild(row));
        
        this.showNotification('Table sorted successfully!', 'success');
    }

    checkEnrollmentDeadlines() {
        const today = new Date();
        const deadlines = [
            { course: 'Computer Science', date: new Date('2025-08-15') },
            { course: 'Summer Session One', date: new Date('2025-07-15') }
        ];
        
        deadlines.forEach(deadline => {
            const daysUntil = Math.ceil((deadline.date - today) / (1000 * 60 * 60 * 24));
            if (daysUntil <= 30 && daysUntil > 0) {
                this.showNotification(
                    `Enrollment deadline for ${deadline.course} is in ${daysUntil} days!`,
                    'warning'
                );
            }
        });
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span class="notification-message">${message}</span>
            <button class="notification-close" aria-label="Close notification">&times;</button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
        
        // Manual close
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });
    }

    showHelpModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h3>Help & Support</h3>
                    <button class="modal-close" aria-label="Close help modal">&times;</button>
                </div>
                <div class="modal-body">
                    <h4>Navigation Tips:</h4>
                    <ul>
                        <li>Use the left sidebar to navigate between sections</li>
                        <li>Click on table headers with ▲ to sort data</li>
                        <li>Use keyboard shortcuts: Alt+H for home, Alt+L for logout</li>
                    </ul>
                    <h4>Contact Support:</h4>
                    <p>Email: <EMAIL><br>Phone: (*************</p>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal handlers
        const closeModal = () => modal.remove();
        modal.querySelector('.modal-close').addEventListener('click', closeModal);
        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeModal();
        });
        
        // Escape key to close
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
    }

    showLoading() {
        if (!this.isLoading) {
            this.isLoading = true;
            const loader = document.createElement('div');
            loader.className = 'loading-overlay';
            loader.innerHTML = '<div class="spinner"></div>';
            document.body.appendChild(loader);
        }
    }

    hideLoading() {
        this.isLoading = false;
        const loader = document.querySelector('.loading-overlay');
        if (loader) loader.remove();
    }

    handleKeyboardNavigation(e) {
        if (e.altKey) {
            switch (e.key) {
                case 'h':
                    e.preventDefault();
                    this.switchSection('student-center');
                    break;
                case 'l':
                    e.preventDefault();
                    this.handleLogout();
                    break;
            }
        }
    }

    loadUserPreferences() {
        // Load user preferences from localStorage
        const preferences = localStorage.getItem('studentPortalPrefs');
        if (preferences) {
            const prefs = JSON.parse(preferences);
            // Apply saved preferences
            console.log('Loaded user preferences:', prefs);
        }
    }

    saveUserPreferences() {
        const preferences = {
            currentSection: this.currentSection,
            timestamp: new Date().toISOString()
        };
        localStorage.setItem('studentPortalPrefs', JSON.stringify(preferences));
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.studentPortal = new StudentPortal();
});

// Handle browser back/forward buttons
window.addEventListener('popstate', (e) => {
    if (e.state && e.state.section) {
        window.studentPortal.switchSection(e.state.section);
    }
});

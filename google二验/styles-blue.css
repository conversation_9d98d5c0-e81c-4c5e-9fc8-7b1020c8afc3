* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    font-size: 12px;
    background-color: #f5f5f5;
}

.container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: linear-gradient(to bottom, #4a90e2, #357abd);
    color: white;
    padding: 8px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo {
    width: 40px;
    height: 30px;
    background-color: white;
    border-radius: 3px;
    object-fit: contain;
    padding: 2px;
}

.university-name {
    font-size: 11px;
    font-weight: bold;
}

.header-right {
    display: flex;
    gap: 5px;
}

.icon-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
}

.icon-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 180px;
    background-color: #e8e8e8;
    border-right: 1px solid #ccc;
    display: flex;
    flex-direction: column;
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    padding: 8px 12px;
    border-bottom: 1px solid #d0d0d0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
}

.nav-item:hover {
    background-color: #ddd;
}

.nav-item.active {
    background-color: #b8d4f0;
    border-left: 3px solid #4a90e2;
}

.nav-icon {
    font-size: 12px;
    width: 16px;
}

.nav-text {
    flex: 1;
}

.nav-subtext {
    font-size: 10px;
    color: #666;
    margin-left: auto;
}

.logout-section {
    margin-top: auto;
    padding: 10px;
}

.logout-btn-sidebar {
    background-color: #d9534f;
    color: white;
    border: none;
    padding: 8px 12px;
    width: 100%;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
}

/* Content Area */
.content {
    flex: 1;
    padding: 15px;
    background-color: white;
    overflow-y: auto;
}

.breadcrumb {
    background: linear-gradient(to bottom, #4a90e2, #357abd);
    color: white;
    padding: 8px 15px;
    margin: -15px -15px 15px -15px;
    font-size: 11px;
    font-weight: bold;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    font-size: 11px;
}

.user-icon {
    font-size: 14px;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 11px;
}

th {
    background-color: #333;
    color: white;
    padding: 8px;
    text-align: left;
    font-size: 10px;
    font-weight: bold;
}

td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
}

.appointments-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.enrollment-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.limits-table {
    background-color: #f5f5f5;
}

.limits-table th {
    background-color: #666;
}

/* Sections */
section {
    margin-bottom: 25px;
}

h2 {
    font-size: 13px;
    margin-bottom: 10px;
    color: #333;
}

h3 {
    font-size: 12px;
    margin-bottom: 8px;
    color: #333;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    font-weight: bold;
}

.btn-primary {
    background-color: #f0ad4e;
    color: white;
}

.btn-secondary {
    background-color: #5bc0de;
    color: white;
}

.btn:hover {
    opacity: 0.9;
}

/* Right Sidebar */
.right-sidebar {
    width: 200px;
    background-color: #f8f8f8;
    border-left: 1px solid #ddd;
    padding: 15px;
}

.sidebar-section {
    margin-bottom: 20px;
}

.sidebar-section h4 {
    font-size: 11px;
    color: #666;
    margin-bottom: 8px;
    font-weight: bold;
}

.sidebar-links {
    list-style: none;
}

.sidebar-links li {
    margin-bottom: 5px;
}

.sidebar-links a {
    color: #4a90e2;
    text-decoration: none;
    font-size: 11px;
}

.sidebar-links a:hover {
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .right-sidebar {
        width: 150px;
    }
}

@media (max-width: 1000px) {
    .right-sidebar {
        display: none;
    }
}

@media (max-width: 800px) {
    .sidebar {
        width: 150px;
    }
    
    .nav-text {
        font-size: 10px;
    }
}
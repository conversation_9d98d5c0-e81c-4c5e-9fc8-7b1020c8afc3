/* Modern CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*:focus {
    outline: 2px solid #4a90e2;
    outline-offset: 2px;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    background-color: #f8fafc;
    color: #334155;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* CSS Custom Properties for consistent theming */
:root {
    --primary-color: #4a90e2;
    --primary-dark: #357abd;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --border-color: #e2e8f0;
    --text-primary: #0f172a;
    --text-secondary: #64748b;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
}

/* Layout Components */
.container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--background-color);
}

/* Header */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 4rem;
    box-shadow: var(--shadow-md);
    position: relative;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo {
    width: 40px;
    height: 30px;
    background-color: white;
    border-radius: 3px;
    object-fit: contain;
    padding: 2px;
}

.university-name {
    font-size: 11px;
    font-weight: bold;
}

.header-right {
    display: flex;
    gap: 5px;
}

.icon-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
}

.icon-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 180px;
    background-color: #e8e8e8;
    border-right: 1px solid #ccc;
    display: flex;
    flex-direction: column;
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    padding: 8px 12px;
    border-bottom: 1px solid #d0d0d0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
}

.nav-item:hover {
    background-color: #ddd;
}

.nav-item.active {
    background-color: #b8d4f0;
    border-left: 3px solid #4a90e2;
}

.nav-icon {
    font-size: 12px;
    width: 16px;
}

.nav-text {
    flex: 1;
}

.nav-subtext {
    font-size: 10px;
    color: #666;
    margin-left: auto;
}

.logout-section {
    margin-top: auto;
    padding: 10px;
}

.logout-btn-sidebar {
    background-color: #d9534f;
    color: white;
    border: none;
    padding: 8px 12px;
    width: 100%;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
}

/* Content Area */
.content {
    flex: 1;
    padding: 15px;
    background-color: white;
    overflow-y: auto;
}

.breadcrumb {
    background: linear-gradient(to bottom, #4a90e2, #357abd);
    color: white;
    padding: 8px 15px;
    margin: -15px -15px 15px -15px;
    font-size: 11px;
    font-weight: bold;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    font-size: 11px;
}

.user-icon {
    font-size: 14px;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 11px;
}

th {
    background-color: #333;
    color: white;
    padding: 8px;
    text-align: left;
    font-size: 10px;
    font-weight: bold;
}

td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
}

.appointments-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.enrollment-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.limits-table {
    background-color: #f5f5f5;
}

.limits-table th {
    background-color: #666;
}

/* Sections */
section {
    margin-bottom: 25px;
}

h2 {
    font-size: 13px;
    margin-bottom: 10px;
    color: #333;
}

h3 {
    font-size: 12px;
    margin-bottom: 8px;
    color: #333;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    font-weight: bold;
}

.btn-primary {
    background-color: #f0ad4e;
    color: white;
}

.btn-secondary {
    background-color: #5bc0de;
    color: white;
}

.btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

.btn-icon {
    margin-right: 0.5rem;
}

.btn-badge {
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 50px;
    margin-left: 0.5rem;
    font-weight: 600;
}

/* Help Text */
.help-text {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: #f8fafc;
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
}

.help-item {
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.help-item:last-child {
    margin-bottom: 0;
}

.help-item strong {
    color: var(--text-primary);
}

/* Quick Stats */
.quick-stats {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-card {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Right Sidebar */
.right-sidebar {
    width: 200px;
    background-color: #f8f8f8;
    border-left: 1px solid #ddd;
    padding: 15px;
}

.sidebar-section {
    margin-bottom: 20px;
}

.sidebar-section h4 {
    font-size: 11px;
    color: #666;
    margin-bottom: 8px;
    font-weight: bold;
}

.sidebar-links {
    list-style: none;
}

.sidebar-links li {
    margin-bottom: 5px;
}

.sidebar-links a {
    color: #4a90e2;
    text-decoration: none;
    font-size: 11px;
}

.sidebar-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.sidebar-link:hover {
    background-color: rgba(74, 144, 226, 0.1);
    text-decoration: none;
}

.link-icon {
    font-size: 1rem;
}

/* Date List */
.date-list {
    margin-top: 0.75rem;
}

.date-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background-color: rgba(74, 144, 226, 0.05);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--primary-color);
}

.date {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.75rem;
    min-width: 2.5rem;
    text-align: center;
}

.date-desc {
    font-size: 0.75rem;
    color: var(--text-secondary);
    flex: 1;
}

/* Interactive Components */
.nav-link {
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: var(--transition-fast);
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0;
}

.nav-link:hover {
    background-color: rgba(74, 144, 226, 0.1);
}

.notification-badge {
    background-color: var(--error-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 50px;
    margin-left: auto;
    min-width: 1.25rem;
    text-align: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    margin-bottom: 1rem;
}

.hamburger {
    display: block;
    width: 1.5rem;
    height: 2px;
    background-color: var(--text-primary);
    position: relative;
    transition: var(--transition-fast);
}

.hamburger::before,
.hamburger::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: var(--text-primary);
    transition: var(--transition-fast);
}

.hamburger::before { top: -0.5rem; }
.hamburger::after { top: 0.5rem; }

.mobile-menu-toggle.active .hamburger {
    background-color: transparent;
}

.mobile-menu-toggle.active .hamburger::before {
    transform: rotate(45deg);
    top: 0;
}

.mobile-menu-toggle.active .hamburger::after {
    transform: rotate(-45deg);
    top: 0;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notifications */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    max-width: 24rem;
    padding: 1rem;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-info {
    background-color: #dbeafe;
    border-left: 4px solid var(--primary-color);
    color: #1e40af;
}

.notification-success {
    background-color: #d1fae5;
    border-left: 4px solid var(--success-color);
    color: #065f46;
}

.notification-warning {
    background-color: #fef3c7;
    border-left: 4px solid var(--warning-color);
    color: #92400e;
}

.notification-error {
    background-color: #fee2e2;
    border-left: 4px solid var(--error-color);
    color: #991b1b;
}

.notification-message {
    flex: 1;
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition-fast);
}

.notification-close:hover {
    opacity: 1;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal {
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 32rem;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: scaleIn 0.2s ease-out;
}

@keyframes scaleIn {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition-fast);
}

.modal-close:hover {
    opacity: 1;
}

.modal-body {
    padding: 1.5rem;
}

.modal-body h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.modal-body ul {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.modal-body li {
    margin-bottom: 0.25rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .right-sidebar {
        width: 150px;
    }
}

@media (max-width: 1000px) {
    .right-sidebar {
        display: none;
    }

    .content {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .sidebar {
        position: fixed;
        top: 4rem;
        left: -100%;
        height: calc(100vh - 4rem);
        z-index: 999;
        transition: left var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }

    .sidebar.mobile-open {
        left: 0;
    }

    .header {
        padding: 0.75rem 1rem;
    }

    .university-name {
        font-size: 0.875rem;
    }

    .nav-text {
        font-size: 0.875rem;
    }

    .content {
        padding: 0.75rem;
    }

    .breadcrumb {
        margin: -0.75rem -0.75rem 1rem -0.75rem;
        padding: 0.75rem;
    }

    .notification {
        top: 0.5rem;
        right: 0.5rem;
        left: 0.5rem;
        max-width: none;
    }

    .modal {
        margin: 1rem;
        width: calc(100% - 2rem);
    }
}

@media (max-width: 480px) {
    .header {
        padding: 0.5rem;
    }

    .logo {
        width: 2rem;
        height: 1.5rem;
    }

    .university-name {
        font-size: 0.75rem;
    }

    .icon-btn {
        width: 2rem;
        height: 2rem;
        font-size: 0.875rem;
    }

    .sidebar {
        width: 100%;
        left: -100%;
    }

    .content {
        padding: 0.5rem;
    }

    table {
        font-size: 0.75rem;
    }

    th, td {
        padding: 0.5rem 0.25rem;
    }
}

/* Print Styles */
@media print {
    .header,
    .sidebar,
    .right-sidebar,
    .action-buttons,
    .notification,
    .modal-overlay,
    .loading-overlay {
        display: none !important;
    }

    .content {
        padding: 0;
        box-shadow: none;
    }

    .main-content {
        display: block;
    }

    table {
        page-break-inside: avoid;
    }

    h2, h3 {
        page-break-after: avoid;
    }
}
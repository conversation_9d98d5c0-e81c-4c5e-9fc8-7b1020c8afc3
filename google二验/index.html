<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="University Student Information System - Portfolio Project by <PERSON><PERSON>">
    <title>Student Portal - Portfolio Project</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #334155;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .hero {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }

        .hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto 2rem;
        }

        .cta-button {
            display: inline-block;
            background: white;
            color: #4a90e2;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1e293b;
        }

        .feature-card p {
            color: #64748b;
        }

        .tech-stack {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 3rem;
        }

        .tech-stack h2 {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 2rem;
            color: #1e293b;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .tech-item {
            text-align: center;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 0.5rem;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tech-item:hover {
            border-color: #4a90e2;
            background: #e0f2fe;
        }

        .tech-item .tech-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .tech-item .tech-name {
            font-weight: 600;
            color: #1e293b;
        }

        .project-info {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }

        .project-info h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #1e293b;
        }

        .project-info p {
            font-size: 1.1rem;
            color: #64748b;
            margin-bottom: 2rem;
        }

        .links {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .link-button {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .link-primary {
            background: #4a90e2;
            color: white;
        }

        .link-secondary {
            background: #e2e8f0;
            color: #475569;
        }

        .link-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }
            
            .hero p {
                font-size: 1rem;
            }
            
            .container {
                padding: 1rem;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <h1>University Student Portal</h1>
            <p>A modern, responsive web application showcasing advanced frontend development skills with interactive user experience and accessibility features.</p>
            <a href="studentinfo.html" class="cta-button">View Live Demo →</a>
        </div>

        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h3>Responsive Design</h3>
                <p>Mobile-first approach with adaptive layouts that work seamlessly across all devices and screen sizes.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3>Interactive UI</h3>
                <p>Dynamic content loading, smooth animations, and real-time notifications for enhanced user experience.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">♿</div>
                <h3>Accessibility First</h3>
                <p>WCAG 2.1 compliant with keyboard navigation, screen reader support, and semantic HTML structure.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🎨</div>
                <h3>Modern Design</h3>
                <p>Clean, professional interface with consistent design system and smooth CSS animations.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🔧</div>
                <h3>Advanced Features</h3>
                <p>Sortable tables, modal dialogs, notification system, and local storage for user preferences.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3>Data Management</h3>
                <p>Interactive enrollment system with real-time updates and comprehensive academic record tracking.</p>
            </div>
        </div>

        <div class="tech-stack">
            <h2>Technologies Used</h2>
            <div class="tech-grid">
                <div class="tech-item">
                    <div class="tech-icon">🌐</div>
                    <div class="tech-name">HTML5</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">🎨</div>
                    <div class="tech-name">CSS3</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">⚡</div>
                    <div class="tech-name">JavaScript ES6+</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">📐</div>
                    <div class="tech-name">CSS Grid</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">📱</div>
                    <div class="tech-name">Responsive Design</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">♿</div>
                    <div class="tech-name">ARIA/WCAG</div>
                </div>
            </div>
        </div>

        <div class="project-info">
            <h2>Portfolio Project</h2>
            <p>This project demonstrates proficiency in modern web development, user experience design, and accessibility standards. Built with clean, maintainable code and attention to performance optimization.</p>
            <div class="links">
                <a href="studentinfo.html" class="link-button link-primary">Live Demo</a>
                <a href="README.md" class="link-button link-secondary">Documentation</a>
                <a href="#" class="link-button link-secondary">Source Code</a>
            </div>
        </div>
    </div>
</body>
</html>

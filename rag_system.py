#!/usr/bin/env python3
"""
RAG (Retrieval-Augmented Generation) 系统实现
使用简单的TF-IDF向量化和余弦相似度进行文档检索
"""

import json
import math
import re
from collections import Counter, defaultdict
from typing import List, Dict, Tuple

class SimpleRAGSystem:
    def __init__(self):
        self.documents = []
        self.tf_idf_vectors = []
        self.vocabulary = set()
        self.idf_scores = {}
    
    def preprocess_text(self, text: str) -> List[str]:
        """文本预处理：转小写、分词、去除标点"""
        text = text.lower()
        # 简单分词（按空格和标点分割），支持中文
        words = []
        import re
        # 分离中文字符和英文单词
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
        english_words = re.findall(r'\b[a-zA-Z]+\b', text)
        
        # 添加中文字符（作为单个词）
        words.extend(chinese_chars)
        # 添加英文单词
        words.extend(english_words)
        
        return words
    
    def add_document(self, doc_id: str, text: str):
        """添加文档到知识库"""
        words = self.preprocess_text(text)
        self.documents.append({
            'id': doc_id,
            'text': text,
            'words': words
        })
        self.vocabulary.update(words)
    
    def build_index(self):
        """构建TF-IDF索引"""
        # 计算IDF
        doc_count = len(self.documents)
        word_doc_count = defaultdict(int)
        
        for doc in self.documents:
            unique_words = set(doc['words'])
            for word in unique_words:
                word_doc_count[word] += 1
        
        for word in self.vocabulary:
            self.idf_scores[word] = math.log(doc_count / (word_doc_count[word] + 1))
        
        # 计算每个文档的TF-IDF向量
        vocab_list = list(self.vocabulary)
        
        for doc in self.documents:
            word_count = Counter(doc['words'])
            doc_length = len(doc['words'])
            
            tf_idf_vector = {}
            for word in vocab_list:
                tf = word_count[word] / doc_length if doc_length > 0 else 0
                tf_idf = tf * self.idf_scores[word]
                tf_idf_vector[word] = tf_idf
            
            self.tf_idf_vectors.append(tf_idf_vector)
    
    def cosine_similarity(self, vec1: Dict[str, float], vec2: Dict[str, float]) -> float:
        """计算余弦相似度"""
        # 获取所有词汇
        all_words = set(vec1.keys()) | set(vec2.keys())
        
        # 计算点积
        dot_product = 0
        for word in all_words:
            dot_product += vec1.get(word, 0) * vec2.get(word, 0)
        
        # 计算向量模长
        norm1 = math.sqrt(sum(val ** 2 for val in vec1.values()) if vec1.values() else 0)
        norm2 = math.sqrt(sum(val ** 2 for val in vec2.values()) if vec2.values() else 0)
        
        if norm1 == 0 or norm2 == 0:
            return 0
        
        return dot_product / (norm1 * norm2)
    
    def retrieve_relevant_docs(self, query: str, top_k: int = 3) -> List[Tuple[str, str, float]]:
        """检索与查询最相关的文档"""
        query_words = self.preprocess_text(query)
        query_word_count = Counter(query_words)
        query_length = len(query_words)
        
        # 构建查询的TF-IDF向量
        query_vector = {}
        for word in self.vocabulary:
            tf = query_word_count[word] / query_length if query_length > 0 else 0
            tf_idf = tf * self.idf_scores.get(word, 0)
            query_vector[word] = tf_idf
        
        # 计算与所有文档的相似度
        similarities = []
        for i, doc_vector in enumerate(self.tf_idf_vectors):
            similarity = self.cosine_similarity(query_vector, doc_vector)
            similarities.append((
                self.documents[i]['id'],
                self.documents[i]['text'],
                similarity
            ))
        
        # 按相似度排序并返回top_k
        similarities.sort(key=lambda x: x[2], reverse=True)
        return similarities[:top_k]
    
    def generate_answer(self, query: str, context_docs: List[Tuple[str, str, float]]) -> str:
        """基于检索到的文档生成答案（简化版本）"""
        if not context_docs:
            return "很抱歉，我没有找到相关信息。"
        
        # 简单的答案生成策略：选择最相关的文档片段
        most_relevant_doc = context_docs[0]
        
        answer = f"基于相关文档信息（相似度: {most_relevant_doc[2]:.3f}）：\n\n"
        answer += f"{most_relevant_doc[1]}\n\n"
        
        if len(context_docs) > 1:
            answer += "其他相关信息：\n"
            for doc_id, text, score in context_docs[1:]:
                answer += f"- {text[:100]}... (相似度: {score:.3f})\n"
        
        return answer
    
    def query(self, question: str, top_k: int = 3) -> str:
        """RAG主流程：检索 + 生成"""
        print(f"用户问题: {question}")
        print("=" * 50)
        
        # 检索相关文档
        relevant_docs = self.retrieve_relevant_docs(question, top_k)
        
        print("检索到的相关文档:")
        for i, (doc_id, text, score) in enumerate(relevant_docs, 1):
            print(f"{i}. 文档ID: {doc_id}, 相似度: {score:.3f}")
            print(f"   内容: {text[:100]}...")
            print()
        
        # 生成答案
        answer = self.generate_answer(question, relevant_docs)
        
        print("生成的答案:")
        print(answer)
        
        return answer

def create_sample_knowledge_base() -> SimpleRAGSystem:
    """创建示例知识库"""
    rag = SimpleRAGSystem()
    
    # 添加示例文档
    documents = [
        ("doc1", "Python是一种高级编程语言，具有简洁易读的语法。Python广泛应用于Web开发、数据科学、人工智能等领域。"),
        ("doc2", "机器学习是人工智能的一个分支，通过算法让计算机从数据中学习模式。常见的机器学习算法包括线性回归、决策树、神经网络等。"),
        ("doc3", "深度学习是机器学习的一个子集，使用多层神经网络来模拟人脑的工作方式。深度学习在图像识别、自然语言处理等任务上表现出色。"),
        ("doc4", "自然语言处理（NLP）是计算机科学和人工智能的一个领域，专注于让计算机理解和生成人类语言。NLP的应用包括机器翻译、文本分析、聊天机器人等。"),
        ("doc5", "数据科学结合了统计学、计算机科学和领域知识，用于从数据中提取洞察。数据科学家使用Python、R等工具进行数据分析和建模。"),
        ("doc6", "Web开发涉及创建和维护网站。前端开发关注用户界面，后端开发处理服务器逻辑。常用的Web开发框架包括Django、Flask、React等。"),
        ("doc7", "云计算是通过互联网提供计算服务，包括服务器、存储、数据库等。主要的云服务提供商有AWS、Azure、Google Cloud等。"),
        ("doc8", "区块链是一种分布式账本技术，具有去中心化、不可篡改的特点。区块链技术最初用于比特币，现在也应用于供应链管理、身份验证等领域。")
    ]
    
    for doc_id, text in documents:
        rag.add_document(doc_id, text)
    
    # 构建索引
    rag.build_index()
    
    return rag

def main():
    """主函数：演示RAG系统"""
    print("正在创建RAG系统和知识库...")
    rag = create_sample_knowledge_base()
    
    print(f"知识库已创建，包含 {len(rag.documents)} 个文档")
    print("=" * 50)
    
    # 测试查询
    test_queries = [
        "什么是Python？",
        "机器学习有哪些算法？",
        "深度学习的应用领域",
        "如何进行数据分析？",
        "云计算的优势是什么？"
    ]
    
    for query in test_queries:
        print(f"\n{'='*60}")
        rag.query(query)
        print(f"{'='*60}\n")
    
    # 交互式查询
    print("\n" + "="*60)
    print("进入交互模式，输入问题进行查询（输入 'quit' 退出）：")
    
    while True:
        user_query = input("\n请输入您的问题: ").strip()
        
        if user_query.lower() in ['quit', 'exit', '退出']:
            break
        
        if user_query:
            print("\n" + "-"*50)
            rag.query(user_query)
            print("-"*50)

if __name__ == "__main__":
    main()
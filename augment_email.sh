#!/usr/bin/env bash
set -euo pipefail

# ====== 外观与工具 ======
bold(){ printf "\033[1m%s\033[0m\n" "$*"; }
info(){ printf "[INFO] %s\n" "$*"; }
ok(){   printf "[ OK ] %s\n" "$*"; }
warn(){ printf "[WARN] %s\n" "$*"; }
err(){  printf "[ERR ] %s\n" "$*" >&2; }

SESSION_ID="${1:-${SESSION_ID:-}}"
if [[ -z "${SESSION_ID:-}" ]]; then
  err "请以参数或环境变量提供 SESSION_ID：  bash $0 '<session_id>'"
  exit 1
fi

FOUND_EMAILS=()
add_email(){
  local e="$1"
  [[ "$e" =~ ^[A-Za-z0-9._%+\-]+@[A-Za-z0-9.\-]+\.[A-Za-z]{2,}$ ]] || return 0
  [[ " ${FOUND_EMAILS[*]} " == *" $e "* ]] || FOUND_EMAILS+=("$e")
}

extract_emails_from_text(){
  # 从 STDIN 抽取邮箱
  grep -Eio '[A-Za-z0-9._%+\-]+@[A-Za-z0-9.\-]+\.[A-Za-z]{2,}' | while read -r line; do
    add_email "$line"
  done
}

have(){ command -v "$1" >/dev/null 2>&1; }

# ====== 办法A：JWT 离线解码 ======
try_jwt_decode(){
  # 形状检测：三段式 x.y.z 即视为 JWT
  if [[ "$SESSION_ID" != *.*.* ]]; then
    warn "看起来不像 JWT（未检测到三段式），跳过离线解码。"
    return 1
  fi
  info "尝试对 session 作 JWT 离线解码（不校验签名）..."
  local py='
import base64, json, sys
t = sys.argv[1]
parts = t.split(".")
if len(parts) < 2:
  sys.exit(2)
def b64u(s):
  s += "=" * (-len(s) % 4)
  return base64.urlsafe_b64decode(s.encode())
payload = json.loads(b64u(parts[1]))
keys = ["email","preferred_username","upn","name","sub"]
for k in keys:
  if k in payload:
    print(f"{k}: {payload[k]}")
print(json.dumps(payload, ensure_ascii=False))
'
  if ! have python3; then
    warn "未检测到 python3，跳过 JWT 解码。"
    return 1
  fi
  local out
  if ! out=$(python3 - <<'PY' "$SESSION_ID" 2>/dev/null || true); then
    warn "JWT 解码失败。"
    return 1
  fi
  if [[ -n "$out" ]]; then
    printf "%s\n" "$out" | extract_emails_from_text
  fi
  if [[ ${#FOUND_EMAILS[@]} -gt 0 ]]; then
    ok "已从 JWT payload 提取邮箱。"
    return 0
  else
    warn "JWT payload 中未发现邮箱字段（可能用的是 sub/内部ID）。"
    return 1
  fi
}

# ====== VS Code 目录候选 ======
add_if_exists(){ [[ -e "$1" ]] && echo "$1"; }
vscode_roots(){
  local os; os="$(uname -s)"
  if [[ "$os" == "Darwin" ]]; then
    add_if_exists "$HOME/Library/Application Support/Code"
    add_if_exists "$HOME/Library/Application Support/Code - Insiders"
  elif [[ "$os" == "Linux" ]]; then
    add_if_exists "$HOME/.config/Code"
    add_if_exists "$HOME/.config/Code - Insiders"
    add_if_exists "$HOME/snap/code/common/.config/Code"
    add_if_exists "$HOME/.var/app/com.visualstudio.code/config/Code"
    add_if_exists "$HOME/.config/VSCodium"
  fi
  # 远程/容器/WSL/SSH（当前机器若是远程端，则这里生效）
  add_if_exists "$HOME/.vscode-server/data"
  add_if_exists "$HOME/.vscode-remote/data"  # 旧少数
}

# ====== 办法B：本地反查 session → 邮箱 ======
# 仅限含 augment 的相关文件，并限制文本类型
scan_text_files_for_email(){
  local folder="$1"; [[ -d "$folder" ]] || return 1
  local found=1

  # 文件候选：包含 augment 名称的路径 / 文件
  mapfile -t files < <(find "$folder" -maxdepth 6 -type f \( -iname "*augment*" -o -path "*/augment*/*" \) 2>/dev/null || true)
  [[ ${#files[@]} -gt 0 ]] || return 1

  # 优先 ripgrep；无则用 grep
  if have rg; then
    # 先定位包含 SESSION_ID 的文件
    mapfile -t hitfiles < <(rg -n --fixed-strings --max-filesize 4M --no-messages "$SESSION_ID" "${files[@]}" | awk -F: '{print $1}' | sort -u)
    if [[ ${#hitfiles[@]} -gt 0 ]]; then
      found=0
      # 在命中文件中提取邮箱样式文本
      rg -n --max-filesize 4M --no-messages -e '[A-Za-z0-9._%+\-]+@[A-Za-z0-9.\-]+\.[A-Za-z]{2,}' "${hitfiles[@]}" | cut -d: -f3- | extract_emails_from_text
    fi
  else
    # grep 兜底：逐个文件查 session，再抓邮箱
    for file in "${files[@]}"; do
      # 仅文本类文件
      if file "$file" | grep -qiE 'text|json|utf-8|ascii'; then
        if grep -qF "$SESSION_ID" "$file" 2>/dev/null; then
          found=0
          grep -Eio '[A-Za-z0-9._%+\-]+@[A-Za-z0-9.\-]+\.[A-Za-z]{2,}' "$file" 2>/dev/null | while read -r e; do add_email "$e"; done
        fi
      fi
    done
  fi
  [[ $found -eq 0 ]]
}

try_local_reverse_lookup(){
  info "基于 VS Code 数据目录反查 session→邮箱（仅文本、仅 augment 相关文件）..."
  local any=1
  while IFS= read -r root; do
    # User/Machine Settings（本地或远程）
    for d in "$root/User" "$root/Machine"; do
      [[ -d "$d" ]] || continue
      scan_text_files_for_email "$d" && any=0
    done
    # 扩展目录（本地或远程）
    for d in "$HOME/.vscode/extensions" "$HOME/.vscode-server/extensions"; do
      [[ -d "$d" ]] || continue
      scan_text_files_for_email "$d" && any=0
    done
  done < <(vscode_roots | sed '/^$/d')

  if [[ $any -eq 0 ]]; then
    ok "已在本地存储中找到与 session 关联的邮箱线索。"
    return 0
  else
    warn "未在本地/远程 VS Code 存储中命中 session。"
    return 1
  fi
}

# ====== 主流程 ======
bold "== Augment：基于 Session ID 提取邮箱 =="
hit=0
try_jwt_decode && hit=1 || true
try_local_reverse_lookup && hit=1 || true

echo
if [[ ${#FOUND_EMAILS[@]} -gt 0 ]]; then
  bold "推测的 Augment 登录邮箱（按可信度）"
  # 这里不强行排序：JWT 提取通常最前；本地反查随后
  for e in "${FOUND_EMAILS[@]}"; do
    echo " - $e"
  done
  echo
  ok "提示：JWT payload（若有）通常最可信；本地反查来自插件状态/日志。"
  exit 0
else
  if [[ $hit -eq 0 ]]; then
    err "未能执行有效检查；请确认在【实际保存该 session 的环境】里运行（例如 WSL/SSH/容器内）。"
  else
    err "未提取到邮箱。可能原因："
    echo "  • 该 session 不是 JWT，且本地存储未保存邮箱字段；"
    echo "  • Augment 使用加密或二进制存储；"
    echo "  • 你运行脚本的环境与保存 session 的环境不一致（本机 vs 远程）。"
    echo
    echo "建议下一步："
    echo "  1) VS Code → Developer: Toggle Developer Tools → Network，查看 Augment 请求响应是否含 email；"
    echo "  2) 若官方提供 whoami / /v1/me 等接口，用 Authorization: Bearer <session> 查询（仅限可信官方域名）；"
    echo "  3) 在保存 session 的那台主机/容器里再次运行本脚本。"
  fi
  exit 2
fi
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Optional, Tuple
import re
import json
from collections import Counter


class GraphNeuralNetwork(nn.Module):
    """Graph Neural Network implementation using PyTorch"""
    
    def __init__(self, input_dim, hidden_dim, output_dim, num_layers=2):
        super(GraphNeuralNetwork, self).__init__()
        self.num_layers = num_layers
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        
        # Graph convolution layers
        self.layers = nn.ModuleList()
        
        # First layer
        self.layers.append(nn.Linear(input_dim, hidden_dim))
        
        # Hidden layers
        for _ in range(num_layers - 2):
            self.layers.append(nn.Linear(hidden_dim, hidden_dim))
        
        # Output layer
        if num_layers > 1:
            self.layers.append(nn.Linear(hidden_dim, output_dim))
        else:
            self.layers[0] = nn.Linear(input_dim, output_dim)
        
        self.dropout = nn.Dropout(0.5)
        self.activation = nn.ReLU()
        
    def forward(self, node_features, adjacency_matrix):
        """
        Forward pass of the GNN
        Args:
            node_features: Node feature matrix [num_nodes, input_dim]
            adjacency_matrix: Adjacency matrix [num_nodes, num_nodes]
        """
        x = node_features
        
        for i, layer in enumerate(self.layers):
            # Graph convolution: A * X * W
            x = torch.mm(adjacency_matrix, x)
            x = layer(x)
            
            # Apply activation and dropout for all layers except the last
            if i < len(self.layers) - 1:
                x = self.activation(x)
                x = self.dropout(x)
        
        return x
    
    def graph_pooling(self, node_embeddings, pool_type='mean'):
        """
        Pool node embeddings to get graph-level representation
        Args:
            node_embeddings: Node embeddings [num_nodes, output_dim]
            pool_type: 'mean', 'max', or 'sum'
        """
        if pool_type == 'mean':
            return torch.mean(node_embeddings, dim=0)
        elif pool_type == 'max':
            return torch.max(node_embeddings, dim=0)[0]
        elif pool_type == 'sum':
            return torch.sum(node_embeddings, dim=0)
        else:
            raise ValueError("pool_type must be 'mean', 'max', or 'sum'")


class SimpleTokenizer:
    """Simple tokenizer for text processing"""
    
    def __init__(self, vocab_size: int = 10000):
        self.vocab_size = vocab_size
        self.vocab: Dict[str, int] = {}
        self.inverse_vocab: Dict[int, str] = {}
        self.special_tokens = {
            '<PAD>': 0,
            '<UNK>': 1,
            '<BOS>': 2,
            '<EOS>': 3,
        }
        self.word_freq = Counter()
        self._initialize_special_tokens()
    
    def _initialize_special_tokens(self):
        for token, idx in self.special_tokens.items():
            self.vocab[token] = idx
            self.inverse_vocab[idx] = token
    
    def _preprocess_text(self, text: str) -> str:
        text = text.lower()
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def _tokenize_text(self, text: str) -> List[str]:
        text = self._preprocess_text(text)
        return text.split()
    
    def build_vocab(self, corpus: List[str]):
        all_tokens = []
        for text in corpus:
            tokens = self._tokenize_text(text)
            all_tokens.extend(tokens)
        
        self.word_freq = Counter(all_tokens)
        
        vocab_items = self.word_freq.most_common(self.vocab_size - len(self.special_tokens))
        
        current_idx = len(self.special_tokens)
        for word, freq in vocab_items:
            if word not in self.vocab:
                self.vocab[word] = current_idx
                self.inverse_vocab[current_idx] = word
                current_idx += 1
    
    def encode(self, text: str, add_special_tokens: bool = True) -> List[int]:
        tokens = self._tokenize_text(text)
        token_ids = []
        
        if add_special_tokens:
            token_ids.append(self.special_tokens['<BOS>'])
        
        for token in tokens:
            token_id = self.vocab.get(token, self.special_tokens['<UNK>'])
            token_ids.append(token_id)
        
        if add_special_tokens:
            token_ids.append(self.special_tokens['<EOS>'])
        
        return token_ids
    
    def decode(self, token_ids: List[int], skip_special_tokens: bool = True) -> str:
        tokens = []
        special_token_ids = set(self.special_tokens.values()) if skip_special_tokens else set()
        
        for token_id in token_ids:
            if token_id in special_token_ids:
                continue
            token = self.inverse_vocab.get(token_id, '<UNK>')
            tokens.append(token)
        
        return ' '.join(tokens)


def create_sample_graph(num_nodes=10, input_dim=16):
    """Create a sample graph for testing"""
    # Random node features
    node_features = torch.randn(num_nodes, input_dim)
    
    # Create adjacency matrix (simple ring graph with some random connections)
    adj_matrix = torch.zeros(num_nodes, num_nodes)
    
    # Add ring connections
    for i in range(num_nodes):
        adj_matrix[i, (i + 1) % num_nodes] = 1
        adj_matrix[(i + 1) % num_nodes, i] = 1
    
    # Add some random connections
    for _ in range(num_nodes // 2):
        i, j = np.random.choice(num_nodes, 2, replace=False)
        adj_matrix[i, j] = 1
        adj_matrix[j, i] = 1
    
    # Add self-loops
    adj_matrix += torch.eye(num_nodes)
    
    # Normalize adjacency matrix
    degree = torch.sum(adj_matrix, dim=1)
    degree_inv_sqrt = torch.pow(degree, -0.5)
    degree_inv_sqrt[degree_inv_sqrt == float('inf')] = 0
    norm_adj = degree_inv_sqrt.unsqueeze(1) * adj_matrix * degree_inv_sqrt.unsqueeze(0)
    
    return node_features, norm_adj


def test_gnn():
    """Test the Graph Neural Network"""
    print("=== Graph Neural Network 测试 ===")
    
    # Create GNN model
    input_dim = 16
    hidden_dim = 32
    output_dim = 8
    num_layers = 3
    
    gnn = GraphNeuralNetwork(input_dim, hidden_dim, output_dim, num_layers)
    print(f"创建GNN模型: input_dim={input_dim}, hidden_dim={hidden_dim}, output_dim={output_dim}")
    print(f"模型参数数量: {sum(p.numel() for p in gnn.parameters())}")
    
    # Create sample data
    num_nodes = 15
    node_features, adjacency_matrix = create_sample_graph(num_nodes, input_dim)
    
    print(f"\n图数据:")
    print(f"节点数量: {num_nodes}")
    print(f"节点特征维度: {node_features.shape}")
    print(f"邻接矩阵形状: {adjacency_matrix.shape}")
    print(f"边数量: {int(torch.sum(adjacency_matrix).item() - num_nodes) // 2}")  # Subtract self-loops and divide by 2
    
    # Forward pass
    print(f"\n前向传播测试:")
    gnn.eval()
    with torch.no_grad():
        node_embeddings = gnn(node_features, adjacency_matrix)
        graph_embedding = gnn.graph_pooling(node_embeddings, 'mean')
    
    print(f"节点嵌入形状: {node_embeddings.shape}")
    print(f"图级别嵌入形状: {graph_embedding.shape}")
    print(f"样本节点嵌入: {node_embeddings[0, :5]}")
    print(f"图级别嵌入: {graph_embedding[:5]}")
    
    # Training simulation
    print(f"\n训练模拟:")
    optimizer = torch.optim.Adam(gnn.parameters(), lr=0.01)
    criterion = nn.MSELoss()
    
    gnn.train()
    losses = []
    
    for epoch in range(10):
        optimizer.zero_grad()
        
        # Forward pass
        node_embeddings = gnn(node_features, adjacency_matrix)
        graph_embedding = gnn.graph_pooling(node_embeddings, 'mean')
        
        # Dummy target for demonstration
        target = torch.randn_like(graph_embedding)
        
        loss = criterion(graph_embedding, target)
        loss.backward()
        optimizer.step()
        
        losses.append(loss.item())
        
        if epoch % 2 == 0:
            print(f"Epoch {epoch + 1:2d}: Loss = {loss.item():.4f}")
    
    print(f"训练完成! 最终损失: {losses[-1]:.4f}")
    
    # Test different pooling methods
    print(f"\n不同池化方法测试:")
    gnn.eval()
    with torch.no_grad():
        node_embeddings = gnn(node_features, adjacency_matrix)
        
        mean_pool = gnn.graph_pooling(node_embeddings, 'mean')
        max_pool = gnn.graph_pooling(node_embeddings, 'max')
        sum_pool = gnn.graph_pooling(node_embeddings, 'sum')
        
        print(f"Mean pooling: {mean_pool[:3]}")
        print(f"Max pooling:  {max_pool[:3]}")
        print(f"Sum pooling:  {sum_pool[:3]}")
    
    print("=== GNN测试完成 ===\n")


def test_tokenizer():
    """Test the tokenizer"""
    print("=== Tokenizer 测试 ===")
    
    tokenizer = SimpleTokenizer(vocab_size=1000)
    
    sample_corpus = [
        "Hello world, this is a test sentence.",
        "Machine learning is fascinating and powerful.",
        "Natural language processing enables computers to understand human language.",
        "Deep learning models can learn complex patterns from data.",
        "Graph neural networks can process structured data effectively.",
        "PyTorch makes deep learning implementation easier.",
        "Tokenization is an important preprocessing step.",
    ]
    
    print("1. 构建词汇表...")
    tokenizer.build_vocab(sample_corpus)
    print(f"词汇表大小: {len(tokenizer.vocab)}")
    print(f"特殊tokens: {tokenizer.special_tokens}")
    
    print("\n2. 编码测试...")
    test_text = "Hello world, graph neural networks are amazing!"
    encoded = tokenizer.encode(test_text)
    print(f"原文: {test_text}")
    print(f"编码: {encoded}")
    
    print("\n3. 解码测试...")
    decoded = tokenizer.decode(encoded)
    print(f"解码: {decoded}")
    
    print("\n4. 高频词展示...")
    print("前8个高频词:")
    for i, (word, freq) in enumerate(tokenizer.word_freq.most_common(8)):
        print(f"  {i+1}. {word}: {freq}")
    
    print("=== Tokenizer测试完成 ===\n")


def test_graph_classification():
    """Test graph classification task"""
    print("=== 图分类任务测试 ===")
    
    # Create GNN for graph classification
    input_dim = 10
    hidden_dim = 16
    output_dim = 8
    num_classes = 3
    
    gnn = GraphNeuralNetwork(input_dim, hidden_dim, output_dim)
    classifier = nn.Linear(output_dim, num_classes)
    
    # Generate multiple graphs
    num_graphs = 20
    graphs = []
    labels = []
    
    for _ in range(num_graphs):
        num_nodes = np.random.randint(5, 15)
        node_features, adj_matrix = create_sample_graph(num_nodes, input_dim)
        graphs.append((node_features, adj_matrix))
        labels.append(np.random.randint(0, num_classes))
    
    labels = torch.tensor(labels, dtype=torch.long)
    
    print(f"生成 {num_graphs} 个图用于分类")
    print(f"类别数: {num_classes}")
    
    # Training
    optimizer = torch.optim.Adam(list(gnn.parameters()) + list(classifier.parameters()), lr=0.01)
    criterion = nn.CrossEntropyLoss()
    
    gnn.train()
    classifier.train()
    
    for epoch in range(15):
        total_loss = 0
        correct = 0
        
        for i, ((node_features, adj_matrix), label) in enumerate(zip(graphs, labels)):
            optimizer.zero_grad()
            
            # Forward pass
            node_embeddings = gnn(node_features, adj_matrix)
            graph_embedding = gnn.graph_pooling(node_embeddings, 'mean')
            logits = classifier(graph_embedding)
            
            loss = criterion(logits.unsqueeze(0), label.unsqueeze(0))
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            pred = torch.argmax(logits).item()
            if pred == label.item():
                correct += 1
        
        accuracy = correct / num_graphs
        avg_loss = total_loss / num_graphs
        
        if epoch % 3 == 0:
            print(f"Epoch {epoch + 1:2d}: Loss = {avg_loss:.4f}, Accuracy = {accuracy:.3f}")
    
    print("=== 图分类测试完成 ===")


if __name__ == "__main__":
    # Run all tests
    test_gnn()
    test_tokenizer()
    test_graph_classification()
    
    print("\n🎉 所有测试完成!")
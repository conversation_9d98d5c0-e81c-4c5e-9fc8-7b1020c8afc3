#!/usr/bin/env python3
"""
循环调用50次最基础模型
"""

import time
import json

def call_basic_model(prompt="Hello", iteration=1):
    """调用基础模型的函数"""
    try:
        # 这里使用一个简单的示例API调用
        # 您可以根据实际需要的API修改这部分
        print(f"第 {iteration} 次调用: {prompt}")
        
        # 模拟API调用延迟
        time.sleep(0.1)
        
        # 模拟返回结果
        result = f"模型响应 {iteration}: 收到提示 '{prompt}'"
        print(f"响应: {result}")
        return result
        
    except Exception as e:
        print(f"第 {iteration} 次调用出错: {e}")
        return None

def main():
    """主函数：循环调用50次"""
    print("开始循环调用基础模型 50 次...")
    
    results = []
    
    for i in range(1, 51):
        print(f"\n--- 第 {i} 次调用 ---")
        result = call_basic_model(f"测试提示 {i}", i)
        
        if result:
            results.append(result)
        
        # 在每10次调用后稍作停顿
        if i % 10 == 0:
            print(f"\n已完成 {i} 次调用，稍作休息...")
            time.sleep(1)
    
    print(f"\n调用完成！总共成功调用 {len(results)} 次")
    
    # 保存结果到文件
    with open('model_call_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print("结果已保存到 model_call_results.json")

if __name__ == "__main__":
    main()